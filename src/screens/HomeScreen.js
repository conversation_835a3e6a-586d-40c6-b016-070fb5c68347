import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, Text, View, FlatList, TouchableOpacity, ScrollView, SafeAreaView, ActivityIndicator, Modal, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Pie<PERSON>hart from '../components/PieChart';
import transactionService from '../services/transactionService';
import hsaDocumentService from '../services/hsaDocumentService';

// Colors for pie chart categories
const categoryColors = {
  'Medical': '#FF6B6B',
  'Dental': '#4ECDC4',
  'Vision': '#45B7D1',
  'Pharmacy': '#FFC857',
  'Supplies': '#96CEB4',
  'Equipment': '#6C88C4',
  'Therapy': '#D499B9',
  'Lab': '#937DC2',
  'Other': '#9D9D9D'
};

// Default account balances before data is loaded
const defaultAccountBalances = [
  { id: '1', type: 'FSA', balance: 0, deadline: 'Mar 15, 2026' },
  { id: '2', type: 'HSA', balance: 0, deadline: 'N/A (Rollover)' },
];

// Generate years from 1950 to 2099
const generateAllYears = () => {
  const years = [];
  for (let year = 1950; year <= 2099; year++) {
    years.push(year);
  }
  return years;
};

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  // State for live HSA data
  const [accountBalances, setAccountBalances] = useState(defaultAccountBalances);
  const [expenseCategoriesPieData, setExpenseCategoriesPieData] = useState([]);
  const [dataLoading, setDataLoading] = useState(true);
  const [receiptsLoading, setReceiptsLoading] = useState(true);
  const [hsaSummary, setHsaSummary] = useState({
    totalExpenses: 0,
    yearlyExpenses: 0,
    yearlyContribution: 0,
  });
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [yearPickerVisible, setYearPickerVisible] = useState(false);
  const [recentReceipts, setRecentReceipts] = useState([]);
  const allYears = generateAllYears();
  
  // Calculate the current year index for initial rendering
  const currentYearIndex = allYears.findIndex(year => year === new Date().getFullYear());
  const visibleYearsCount = 7; // Number of years visible in the picker
  
  const renderReceiptItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.benefitCard}
      onPress={() => navigation.navigate('HSADocumentDetail', { documentId: item.id })}
    >
      <Text style={styles.benefitTitle}>{item.title}</Text>
      <View style={styles.benefitDetails}>
        <View style={styles.categoryContainer}>
          <Text style={[styles.categoryPill, { backgroundColor: categoryColors[item.category] || '#9D9D9D' }]}>
            {item.category}
          </Text>
        </View>
        <View style={styles.amountContainer}>
          <Text style={styles.amountText}>${parseFloat(item.amount).toFixed(2)}</Text>
          <Text style={[styles.statusBadge, { 
            backgroundColor: 
              item.status === 'reimbursed' ? 'rgba(76, 175, 80, 0.15)' : 'rgba(33, 150, 243, 0.15)',
            color:
              item.status === 'reimbursed' ? '#2e7d32' : '#0277bd',
          }]}>
            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </Text>
        </View>
        <Text style={styles.dateText}>{new Date(item.expense_date).toLocaleDateString()}</Text>
      </View>
    </TouchableOpacity>
  );
  
  // Fetch HSA data and monthly chart data
  const loadHSAData = async (year = selectedYear) => {
    setDataLoading(true);
    try {
      // Check if we can get the data
      const [summaryData, categoryData] = await Promise.all([
        transactionService.getHSASummary(),
        transactionService.getCategorySpendingData(year)
      ]);
      
      // If API calls return null, we might have authentication issues
      if (!summaryData && !categoryData) {
        console.log('HSA API calls failed, possible authentication issue');
        setDataLoading(false);
        return;
      }
      
      // Process the data we got (even if partial)
      if (summaryData) {
        setHsaSummary(summaryData);
        
        // Update HSA balance in accountBalances
        setAccountBalances(prev => prev.map(account => 
          account.type === 'HSA' 
            ? { ...account, balance: summaryData.totalExpenses } 
            : account
        ));
      }

      // Process category data for pie chart
      if (categoryData && categoryData.length > 0) {
        const formattedData = categoryData.map(item => ({
          value: item.amount,
          label: item.name,
          color: categoryColors[item.name] || '#' + Math.floor(Math.random()*********).toString(16) // Random color fallback
        }));
        // Sort by value for better visual representation
        formattedData.sort((a, b) => b.value - a.value);
        setExpenseCategoriesPieData(formattedData);
      } else {
        // Set empty array if no data
        setExpenseCategoriesPieData([]);
      }
    } catch (error) {
      console.error('Error loading HSA data:', error);
    } finally {
      setDataLoading(false);
    }
  };

  // Fetch recent receipts for the current year
  const loadRecentReceipts = async (year = selectedYear) => {
    setReceiptsLoading(true);
    try {
      // Get all HSA documents
      const documents = await transactionService.getAllHSADocuments();
      
      // Filter documents for the current year
      const currentYearDocuments = documents.filter(doc => {
        const expenseDate = new Date(doc.expense_date);
        return expenseDate.getFullYear() === parseInt(year);
      });
      
      // Sort by date (newest first)
      currentYearDocuments.sort((a, b) => new Date(b.expense_date) - new Date(a.expense_date));
      
      // Format documents for display
      const formattedReceipts = currentYearDocuments.map(doc => ({
        id: doc.id,
        title: doc.title || doc.file_name || 'Untitled Receipt',
        category: doc.category || 'Medical',
        expense_date: doc.expense_date,
        amount: doc.amount || 0,
        status: doc.status || 'pending'
      }));
      
      setRecentReceipts(formattedReceipts);
    } catch (error) {
      console.error('Error loading recent receipts:', error);
      setRecentReceipts([]);
    } finally {
      setReceiptsLoading(false);
    }
  };

  // Load data when component mounts or when year changes
  useEffect(() => {
    loadHSAData(selectedYear);
    loadRecentReceipts(selectedYear);
  }, [selectedYear]);

  // Handle year selection
  const handleYearChange = (year) => {
    setSelectedYear(year);
    setYearPickerVisible(false);
  };

  // Navigate to HSA Management screen
  const navigateToHSAManagement = (accountType) => {
    if (accountType === 'HSA') {
      navigation.navigate('HSAManagement');
    }
  };

  // Create a subset of years centered around the selected year for better performance
  const getVisibleYears = () => {
    const selectedYearIndex = allYears.findIndex(year => year === selectedYear);
    const startIndex = Math.max(0, selectedYearIndex - Math.floor(visibleYearsCount / 2));
    const endIndex = Math.min(allYears.length - 1, startIndex + visibleYearsCount - 1);
    
    return allYears.slice(startIndex, endIndex + 1);
  };

  // Year picker modal
  const renderYearPickerModal = () => (
    <Modal
      visible={yearPickerVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setYearPickerVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.yearPickerContainer}>
          <View style={styles.yearPickerHeader}>
            <Text style={styles.yearPickerTitle}>Select Year</Text>
            <TouchableOpacity 
              style={styles.closeButtonContainer}
              onPress={() => setYearPickerVisible(false)}
            >
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.yearPickerContent}>
            {/* Decades quick selector */}
            <View style={styles.decadesSelector}>
              {[1950, 1970, 1990, 2010, 2030, 2050, 2070, 2090].map(decade => (
                <TouchableOpacity
                  key={decade}
                  style={[
                    styles.decadeButton,
                    selectedYear >= decade && selectedYear < decade + 20 && styles.decadeButtonActive
                  ]}
                  onPress={() => setSelectedYear(decade)}
                >
                  <Text style={styles.decadeButtonText}>{decade}s</Text>
                </TouchableOpacity>
              ))}
            </View>
            
            {/* Year grid */}
            <View style={styles.yearGrid}>
              {allYears
                .filter(year => year >= Math.floor(selectedYear / 10) * 10 && year < Math.floor(selectedYear / 10) * 10 + 10)
                .map(year => (
                  <TouchableOpacity
                    key={year}
                    style={[
                      styles.yearGridItem,
                      selectedYear === year && styles.yearGridItemSelected
                    ]}
                    onPress={() => handleYearChange(year)}
                  >
                    <Text
                      style={[
                        styles.yearGridItemText,
                        selectedYear === year && styles.yearGridItemTextSelected,
                        year === new Date().getFullYear() && styles.currentYearText
                      ]}
                    >
                      {year}
                    </Text>
                  </TouchableOpacity>
                ))
              }
            </View>
          </View>
          
          <View style={styles.yearPickerActions}>
            <TouchableOpacity
              style={styles.yearPickerButton}
              onPress={() => handleYearChange(new Date().getFullYear())}
            >
              <Text style={styles.yearPickerButtonText}>Current Year</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.yearPickerButton, styles.yearPickerPrimaryButton]}
              onPress={() => setYearPickerVisible(false)}
            >
              <Text style={[styles.yearPickerButtonText, styles.yearPickerPrimaryButtonText]}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Benefit Vault</Text>
          <TouchableOpacity style={styles.headerButton} onPress={() => navigation.navigate('UploadReceipt')}>
            <Text style={styles.headerButtonText}>+ Add</Text>
          </TouchableOpacity>
        </View>
        
        {/* Account Balance Cards */}
        <View style={styles.balanceCardsContainer}>
          {accountBalances.map((account) => (
            <TouchableOpacity
              key={account.id}
              activeOpacity={account.type === 'HSA' ? 0.7 : 1}
              onPress={() => navigateToHSAManagement(account.type)}
              style={styles.balanceCardWrapper}
            >
              <LinearGradient
                colors={account.type === 'HSA' ? ['#5D69BE', '#4C6FFF'] : ['#43A047', '#66BB6A']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.balanceCard}
              >
                <View style={styles.balanceCardContent}>
                  <Text style={styles.balanceCardLabel}>{account.type} Balance</Text>
                  <Text style={styles.balanceCardAmount}>${account.balance.toLocaleString()}</Text>
                  <Text style={styles.balanceCardDeadline}>
                    {account.type === 'FSA' ? `Expires: ${account.deadline}` : 'Never expires'}
                  </Text>
                  {account.type === 'HSA' && (
                    <View style={styles.viewDetailsContainer}>
                      <Text style={styles.viewDetailsText}>View Details</Text>
                    </View>
                  )}
                </View>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Expense Categories Pie Chart */}
        <View style={styles.chartContainer}>
          <View style={styles.chartHeaderContainer}>
            <Text style={styles.sectionTitle}>Expenses</Text>
            <TouchableOpacity 
              style={styles.yearSelectorButton}
              onPress={() => setYearPickerVisible(true)}
            >
              <Text style={styles.yearSelectorText}>{selectedYear}</Text>
              <Text style={styles.yearSelectorIcon}>▼</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.pieChartContainer}>
            {dataLoading ? (
              <ActivityIndicator size="large" color="#4C6FFF" />
            ) : (
              <PieChart data={expenseCategoriesPieData} size={240} />
            )}
          </View>
          <View style={styles.summaryStats}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Expenses:</Text>
              <Text style={styles.summaryValue}>${expenseCategoriesPieData.reduce((sum, item) => sum + item.value, 0).toFixed(2)}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Receipts Tracked:</Text>
              <Text style={styles.summaryValue}>{recentReceipts.length}</Text>
            </View>
          </View>
        </View>
        
        {/* Recent Receipts */}
        <View style={styles.recentContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Receipts</Text>
            <TouchableOpacity onPress={() => navigation.navigate('HSADocuments')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          {receiptsLoading ? (
            <ActivityIndicator size="large" color="#4C6FFF" style={{padding: 20}} />
          ) : recentReceipts.length > 0 ? (
            <FlatList
              data={recentReceipts.slice(0, 3)}
              renderItem={renderReceiptItem}
              keyExtractor={item => item.id.toString()}
              contentContainerStyle={styles.listContainer}
              showsVerticalScrollIndicator={false}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyStateContainer}>
              <Text style={styles.emptyStateText}>No receipts available for {selectedYear}</Text>
            </View>
          )}
        </View>
      </ScrollView>
      
      {/* Year Picker Modal */}
      {renderYearPickerModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F9FAFC',
  },
  scrollContent: {
    paddingBottom: 30,
  },
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1A2138',
    letterSpacing: 0.3,
  },
  headerButton: {
    backgroundColor: '#4C6FFF',
    paddingHorizontal: 18,
    paddingVertical: 12,
    borderRadius: 14,
    shadowColor: '#4C6FFF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  headerButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 15,
  },
  balanceCardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  balanceCardWrapper: {
    flex: 1,
    marginHorizontal: 6,
  },
  balanceCard: {
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.1,
    shadowRadius: 15,
    elevation: 5,
  },
  balanceCardContent: {
    padding: 20,
    minHeight: 130,
  },
  balanceCardLabel: {
    color: 'rgba(255, 255, 255, 0.85)',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  balanceCardAmount: {
    color: '#FFFFFF',
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 10,
  },
  balanceCardDeadline: {
    color: 'rgba(255, 255, 255, 0.85)',
    fontSize: 13,
    fontWeight: '400',
  },
  viewDetailsContainer: {
    marginTop: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  viewDetailsText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#1A2138',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.06,
    shadowRadius: 16,
    elevation: 3,
  },
  chartHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  yearSelectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F5F9',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  yearSelectorText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginRight: 8,
  },
  yearSelectorIcon: {
    fontSize: 12,
    color: '#64748B',
  },
  pieChartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
    minHeight: 240,
  },
  summaryStats: {
    backgroundColor: '#F8FAFF',
    borderRadius: 16,
    padding: 20,
    marginTop: 10,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 4,
  },
  summaryLabel: {
    color: '#64748B',
    fontSize: 16,
  },
  summaryValue: {
    color: '#1E293B',
    fontWeight: '600',
    fontSize: 16,
  },
  recentContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 24,
    marginBottom: 20,
    shadowColor: '#1A2138',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.06,
    shadowRadius: 16,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A2138',
  },
  seeAllText: {
    fontSize: 14,
    color: '#4C6FFF',
    fontWeight: '600',
  },
  listContainer: {
    paddingTop: 8,
  },
  benefitCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#1A2138',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.03)',
  },
  benefitTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#1A2138',
  },
  benefitDetails: {
    flexDirection: 'column',
    gap: 8,
  },
  categoryContainer: {
    marginBottom: 4,
  },
  categoryPill: {
    fontSize: 13,
    fontWeight: '500',
    color: '#FFFFFF',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  amountContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  amountText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1A2138',
  },
  statusBadge: {
    fontSize: 14,
    fontWeight: '600',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  dateText: {
    fontSize: 13,
    color: '#64748B',
  },
  emptyStateContainer: {
    padding: 30,
    alignItems: 'center',
  },
  emptyStateText: {
    color: '#94A3B8',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  yearPickerContainer: {
    width: SCREEN_WIDTH * 0.85,
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  yearPickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  yearPickerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A2138',
  },
  closeButtonContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    fontSize: 18,
    color: '#64748B',
    fontWeight: '600',
  },
  yearPickerContent: {
    padding: 16,
  },
  decadesSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  decadeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginBottom: 8,
    backgroundColor: '#F1F5F9',
  },
  decadeButtonActive: {
    backgroundColor: '#EEF2FF',
  },
  decadeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  yearGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  yearGridItem: {
    width: '20%',
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  yearGridItemSelected: {
    backgroundColor: '#EEF2FF',
    borderRadius: 8,
  },
  yearGridItemText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#64748B',
  },
  yearGridItemTextSelected: {
    color: '#4C6FFF',
    fontWeight: '700',
  },
  currentYearText: {
    textDecorationLine: 'underline',
  },
  yearPickerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
  },
  yearPickerButton: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  yearPickerPrimaryButton: {
    backgroundColor: '#4C6FFF',
  },
  yearPickerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#64748B',
  },
  yearPickerPrimaryButtonText: {
    color: '#FFFFFF',
  },
});

export default HomeScreen;
